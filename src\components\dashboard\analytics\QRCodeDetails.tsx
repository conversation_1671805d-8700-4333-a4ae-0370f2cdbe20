import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { Button } from '../../ui/button';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../../ui/dialog';
import {
  QrCode,
  Calendar,
  Eye,
  ExternalLink,
  Copy,
  Globe,
  Mail,
  Wifi,
  FileText,
  Phone,
  MapPin,
  RefreshCw,
  Edit,
  Save
} from 'lucide-react';
import { formatDateInUserTimezone, formatTimeAgoInUserTimezone } from '../../../lib/api-utils';

interface QRCodeDetailsProps {
  qrCodeId: string;
}

interface QRCodeInfo {
  id: string;
  name: string;
  content_type: string;
  data: string;
  original_url?: string;
  email_address?: string;
  wifi_ssid?: string;
  phone_number?: string;
  custom_slug?: string;
  dynamic: boolean;
  created_at: string;
  total_scans: number;
  recent_scans_24h: number;
  last_scan_time?: string;
  tracking_domain?: string;
}

export const QRCodeDetails: React.FC<QRCodeDetailsProps> = ({ qrCodeId }) => {
  const [qrCodeInfo, setQRCodeInfo] = useState<QRCodeInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  // Edit functionality state
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editForm, setEditForm] = useState({ name: '', original_url: '' });
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateError, setUpdateError] = useState<string | null>(null);

  const fetchQRCodeDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/qr-codes/${qrCodeId}/details`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch QR code details');
      }

      const data = await response.json();
      setQRCodeInfo(data as unknown as QRCodeInfo);
      setError(null);
    } catch (err) {
      console.error('Error fetching QR code details:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch QR code details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQRCodeDetails();
  }, [qrCodeId]);

  const getContentTypeIcon = (contentType: string) => {
    switch (contentType.toLowerCase()) {
      case 'url':
        return <Globe className="h-5 w-5" />;
      case 'email':
        return <Mail className="h-5 w-5" />;
      case 'wifi':
        return <Wifi className="h-5 w-5" />;
      case 'phone':
        return <Phone className="h-5 w-5" />;
      case 'text':
        return <FileText className="h-5 w-5" />;
      case 'location':
        return <MapPin className="h-5 w-5" />;
      default:
        return <QrCode className="h-5 w-5" />;
    }
  };

  const getContentTypeColor = (contentType: string) => {
    switch (contentType.toLowerCase()) {
      case 'url':
        return 'bg-blue-100 text-blue-800';
      case 'email':
        return 'bg-green-100 text-green-800';
      case 'wifi':
        return 'bg-purple-100 text-purple-800';
      case 'phone':
        return 'bg-orange-100 text-orange-800';
      case 'text':
        return 'bg-gray-100 text-gray-800';
      case 'location':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleCopyData = async () => {
    if (!qrCodeInfo?.original_url) return;

    try {
      await navigator.clipboard.writeText(qrCodeInfo.original_url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleEditClick = () => {
    if (qrCodeInfo) {
      setEditForm({
        name: qrCodeInfo.name || '',
        original_url: qrCodeInfo.original_url || ''
      });
      setUpdateError(null);
      setIsEditDialogOpen(true);
    }
  };

  const handleEditSubmit = async () => {
    if (!qrCodeInfo || !editForm.name.trim() || !editForm.original_url.trim()) {
      setUpdateError('Name and redirect URL are required');
      return;
    }

    setIsUpdating(true);
    setUpdateError(null);

    try {
      const response = await fetch(`/api/qr-codes/${qrCodeId}`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: editForm.name.trim(),
          original_url: editForm.original_url.trim()
        })
      });

      const result = await response.json() as { success: boolean; error?: string; data?: any };

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to update QR code');
      }

      // Update the local state with the new data
      setQRCodeInfo(prev => prev ? {
        ...prev,
        name: editForm.name.trim(),
        original_url: editForm.original_url.trim()
      } : null);

      setIsEditDialogOpen(false);

      // Optionally refresh the data from server
      fetchQRCodeDetails();

    } catch (err) {
      console.error('Error updating QR code:', err);
      setUpdateError(err instanceof Error ? err.message : 'Failed to update QR code');
    } finally {
      setIsUpdating(false);
    }
  };

  // Use timezone-aware formatting functions from api-utils

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardHeader className="p-4 sm:p-6">
          <div className="h-5 sm:h-6 bg-gray-200 rounded w-1/3"></div>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-3">
            <div className="lg:col-span-2 space-y-3 sm:space-y-4">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              <div className="h-16 sm:h-20 bg-gray-200 rounded"></div>
            </div>
            <div className="space-y-3 sm:space-y-4">
              <div className="h-16 sm:h-20 bg-gray-200 rounded"></div>
              <div className="h-16 sm:h-20 bg-gray-200 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !qrCodeInfo) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8 sm:py-12 px-4">
          <div className="text-center max-w-md mx-auto">
            <QrCode className="w-10 h-10 sm:w-12 sm:h-12 text-gray-400 mx-auto mb-3 sm:mb-4" />
            <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">Failed to Load QR Code Details</h3>
            <p className="text-gray-600 mb-4 text-sm sm:text-base">{error}</p>
            <Button onClick={fetchQRCodeDetails} variant="outline" className="h-9 sm:h-10">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="p-4 sm:p-6">
        <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
          <QrCode className="h-5 w-5 sm:h-6 sm:w-6" />
          <span>QR Code Details</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4 sm:p-6">
        <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-3">
          {/* QR Code Information */}
          <div className="lg:col-span-2 space-y-4 sm:space-y-6">
            <div>
              <div className="flex items-center justify-start mb-3 sm:mb-4">
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 truncate">
                  {qrCodeInfo.name.length > 30 ? `${qrCodeInfo.name.slice(0, 30)}...` : qrCodeInfo.name}
                </h3>
              </div>

              <div className="space-y-3 sm:space-y-4">
                <div className="p-3 sm:p-4 bg-gray-50 rounded-lg">
                  <div className="space-y-3">
                    <div>
                      <p className="text-xs sm:text-sm font-medium text-gray-700 mb-2">
                        Redirect URL (Your QR will redirect to this URL)
                      </p>
                      <p className="text-sm sm:text-base text-gray-900 break-all" title={qrCodeInfo.data}>
                        {qrCodeInfo.original_url}
                      </p>
                    </div>

                    {/* Mobile: Stack buttons vertically */}
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleEditClick}
                        className="flex items-center justify-center space-x-1 h-9"
                      >
                        <Edit className="h-3 w-3" />
                        <span>Edit</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCopyData}
                        className="flex items-center justify-center space-x-1 h-9"
                      >
                        <Copy className="h-3 w-3" />
                        <span>{copied ? 'Copied!' : 'Copy'}</span>
                      </Button>
                      {qrCodeInfo.content_type === 'url' && qrCodeInfo.original_url && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(qrCodeInfo.original_url, '_blank')}
                          className="flex items-center justify-center space-x-1 h-9"
                        >
                          <ExternalLink className="h-3 w-3" />
                          <span>Visit</span>
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {qrCodeInfo.custom_slug && (
                  <div className="p-3 sm:p-4 bg-blue-50 rounded-lg">
                    <p className="text-xs sm:text-sm font-medium text-blue-700 mb-1">Short URL</p>
                    <p className="text-sm sm:text-base text-blue-900 break-all">
                      https://{qrCodeInfo.tracking_domain || 'qr.qranalytica.com'}/{qrCodeInfo.custom_slug}
                    </p>
                  </div>
                )}

                <div className="grid gap-3 grid-cols-1 sm:grid-cols-2">
                  <div className="p-3 sm:p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Calendar className="h-4 w-4 text-gray-600" />
                      <p className="text-xs sm:text-sm font-medium text-gray-700">Created</p>
                    </div>
                    <p className="text-sm sm:text-base text-gray-900">{formatDateInUserTimezone(qrCodeInfo.created_at)}</p>
                  </div>

                  {qrCodeInfo.last_scan_time && (
                    <div className="p-3 sm:p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <Eye className="h-4 w-4 text-gray-600" />
                        <p className="text-xs sm:text-sm font-medium text-gray-700">Last Scan</p>
                      </div>
                      <p className="text-sm sm:text-base text-gray-900">{formatTimeAgoInUserTimezone(qrCodeInfo.last_scan_time)}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="space-y-3 sm:space-y-4">
            <div className="p-3 sm:p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-xs sm:text-sm font-medium text-blue-700">Total Scans</p>
                  <p className="text-xl sm:text-2xl font-bold text-blue-900">{qrCodeInfo.total_scans.toLocaleString()}</p>
                </div>
                <div className="p-2 sm:p-3 bg-blue-200 rounded-full flex-shrink-0">
                  <Eye className="h-5 w-5 sm:h-6 sm:w-6 text-blue-700" />
                </div>
              </div>
            </div>

            <div className="p-3 sm:p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-xs sm:text-sm font-medium text-green-700">Last 24 Hours</p>
                  <p className="text-xl sm:text-2xl font-bold text-green-900">{qrCodeInfo.recent_scans_24h.toLocaleString()}</p>
                </div>
                <div className="p-2 sm:p-3 bg-green-200 rounded-full flex-shrink-0">
                  <Calendar className="h-5 w-5 sm:h-6 sm:w-6 text-green-700" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px] mx-4 sm:mx-0">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Edit QR Code</DialogTitle>
            <DialogDescription className="text-sm sm:text-base">
              Update the name and redirect URL for your QR code.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name" className="text-sm font-medium">QR Code Name</Label>
              <Input
                id="edit-name"
                value={editForm.name}
                onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter QR code name"
                disabled={isUpdating}
                className="h-10"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="edit-url" className="text-sm font-medium">Redirect URL</Label>
              <Input
                id="edit-url"
                type="url"
                value={editForm.original_url}
                onChange={(e) => setEditForm(prev => ({ ...prev, original_url: e.target.value }))}
                placeholder="https://example.com"
                disabled={isUpdating}
                className="h-10"
              />
            </div>

            {updateError && (
              <div className="text-sm text-red-600 bg-red-50 p-3 rounded-lg border border-red-200">
                {updateError}
              </div>
            )}
          </div>

          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              disabled={isUpdating}
              className="w-full sm:w-auto h-10"
            >
              Cancel
            </Button>
            <Button
              onClick={handleEditSubmit}
              disabled={isUpdating || !editForm.name.trim() || !editForm.original_url.trim()}
              className="w-full sm:w-auto h-10"
            >
              {isUpdating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default QRCodeDetails;
