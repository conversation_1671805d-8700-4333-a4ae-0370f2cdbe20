import type { APIRoute } from 'astro';
import { getUserIdFromRequest, isAuthenticated } from '../../../lib/auth-utils';
import { getUserSubscription, getTrialStatus } from '../../../lib/subscription-utils';
import { getDatabase } from '../../../lib/database';

export const prerender = false;

interface StartTrialResponse {
  success: boolean;
  data?: {
    trial_start_date: string;
    trial_end_date: string;
    trial_days_remaining: number;
    message: string;
  };
  error?: string;
}

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Check authentication
    const userId = getUserIdFromRequest(request);
    const isUserAuthenticated = isAuthenticated(request);

    if (!userId || !isUserAuthenticated) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User must be logged in to start trial'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user subscription information
    const userSubscription = await getUserSubscription(db, userId);
    
    if (!userSubscription) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User subscription information not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check current trial status
    const trialStatus = getTrialStatus(userSubscription);

    // If user already has an active trial
    if (trialStatus.isTrialActive) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User already has an active trial'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // If user already used their trial
    if (trialStatus.hasTrialStarted && trialStatus.isTrialExpired) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Trial period has already been used'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // If user already has a professional subscription
    if (userSubscription.subscription_plan === 'professional' && 
        userSubscription.subscription_status === 'active') {
      return new Response(JSON.stringify({
        success: false,
        error: 'User already has an active Professional subscription'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Calculate trial dates (7 days from now)
    const trialStartDate = new Date();
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + 7);

    // Update user with trial information
    try {
      const result = await db
        .prepare(`
          UPDATE users 
          SET trial_start_date = ?, 
              trial_end_date = ?,
              subscription_plan = 'professional',
              subscription_status = 'trial',
              updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `)
        .bind(
          trialStartDate.toISOString(),
          trialEndDate.toISOString(),
          userId
        )
        .run();

      if (!result.success) {
        throw new Error('Failed to update user trial information');
      }

      console.log('Trial started for user:', { 
        userId, 
        trialStartDate: trialStartDate.toISOString(),
        trialEndDate: trialEndDate.toISOString()
      });

      const response: StartTrialResponse = {
        success: true,
        data: {
          trial_start_date: trialStartDate.toISOString(),
          trial_end_date: trialEndDate.toISOString(),
          trial_days_remaining: 7,
          message: 'Your 7-day free trial has started! You now have access to all Professional features.'
        }
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { 
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      });

    } catch (dbError) {
      console.error('Database error starting trial:', dbError);
      throw dbError;
    }

  } catch (error) {
    console.error('Error starting trial:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to start trial'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
