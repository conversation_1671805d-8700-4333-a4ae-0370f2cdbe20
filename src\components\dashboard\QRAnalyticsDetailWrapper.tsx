import React, { useState, useEffect } from "react";
import { QRAnalyticsDetail } from "./QRAnalyticsDetail";
import { UpgradeOverlay } from "./UpgradeOverlay";
import { useAuthStore } from "../../stores/authStore";

interface SubscriptionStatusResponse {
  success: boolean;
  data?: {
    subscription_plan: string;
    subscription_status: string;
    trial_status: {
      hasTrialStarted: boolean;
      isTrialActive: boolean;
      isTrialExpired: boolean;
      trialDaysRemaining: number;
      trialEndDate: string | null;
      canAccessPremiumFeatures: boolean;
    };
    can_access_premium: boolean;
  };
  error?: string;
}

interface QRAnalyticsDetailWrapperProps {
  qrCodeId: string;
}

export const QRAnalyticsDetailWrapper: React.FC<
  QRAnalyticsDetailWrapperProps
> = ({ qrCodeId }) => {
  const [showUpgradeOverlay, setShowUpgradeOverlay] = useState(false);
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionStatusResponse['data'] | null>(null);
  const [loading, setLoading] = useState(true);
  const { session, status, signIn } = useAuthStore();

  useEffect(() => {
    const checkSubscriptionStatus = async () => {
      if (status !== 'authenticated' || !session) {
        setLoading(false);
        return;
      }

      try {
        // First, check subscription status
        const response = await fetch('/api/subscription-status', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (response.ok) {
          const result: SubscriptionStatusResponse = await response.json();

          if (result.success && result.data) {
            setSubscriptionData(result.data);

            // Show upgrade overlay if user doesn't have premium access
            if (!result.data.can_access_premium) {
              setShowUpgradeOverlay(true);
            } else {
              setShowUpgradeOverlay(false);
            }
          }
        } else if (response.status === 401) {
          // User not authenticated
          setShowUpgradeOverlay(false);
        } else {
          console.error('Error checking subscription status:', response.status);
        }
      } catch (error) {
        console.error('Error checking subscription status:', error);
      } finally {
        setLoading(false);
      }
    };

    checkSubscriptionStatus();
  }, [qrCodeId, session, status]);

  const handleBack = () => {
    window.location.href = "/dashboard";
  };

  
  // Show login UI when user is not authenticated
  if (status === 'unauthenticated') {
    return (
      <div className="min-h-[60vh] flex items-center justify-center px-4">
        <div className="text-center max-w-md mx-auto p-4 sm:p-8">
          {/* Login illustration */}
          <div className="w-16 h-16 sm:w-24 sm:h-24 mx-auto mb-4 sm:mb-6 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 sm:w-12 sm:h-12 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>

          <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3">Welcome to QRAnalytica</h2>
          <p className="text-gray-600 mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base">
            Sign in to view your QR code analytics.
          </p>

          <div className="flex flex-col gap-3 justify-center">
            <button
              onClick={signIn}
              className="w-full px-4 sm:px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-200 transform hover:scale-105 shadow-lg flex items-center justify-center gap-2 text-sm sm:text-base"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" viewBox="0 0 24 24">
                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Sign in with Google
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show upgrade overlay if trial expired
  if (showUpgradeOverlay) {
    return (
      <UpgradeOverlay
        isVisible={true}
        title="Upgrade to Professional"
        description={
          subscriptionData?.trial_status?.isTrialExpired
            ? "Your 7-day free trial has expired. Upgrade to Professional to continue accessing premium analytics features."
            : "Premium access required. Upgrade to Professional to access advanced analytics features."
        }
        showCloseButton={false}
      />
    );
  }
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[300px] sm:min-h-[400px] px-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600 mx-auto mb-3 sm:mb-4"></div>
          <p className="text-gray-600 text-sm sm:text-base">Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (!qrCodeId) return <div>QR Code not found</div>;

  // Only render QRAnalyticsDetail if user has premium access
  if (subscriptionData?.can_access_premium) {
    return (
      <QRAnalyticsDetail
        qrCodeId={qrCodeId}
        onBack={handleBack}
      />
    );
  }

  // If we reach here, user doesn't have premium access but overlay isn't showing
  // This shouldn't happen, but as a fallback, show a simple message
  return (
    <div className="flex items-center justify-center min-h-[300px] sm:min-h-[400px] px-4">
      <div className="text-center">
        <p className="text-gray-600 text-sm sm:text-base">Loading subscription status...</p>
      </div>
    </div>
  );
};

export default QRAnalyticsDetailWrapper;
