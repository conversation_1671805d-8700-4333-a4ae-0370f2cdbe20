---
import Layout from '../layouts/Layout.astro';
import { DynamicPricingButton } from '../components/pricing/DynamicPricingButton';

const title = "Simple, Transparent Pricing - QRAnalytica";
const description = "Choose the perfect plan for your QR code analytics needs. Start free, upgrade when ready.";
const canonicalURL = "https://qranalytica.com/pricing";

// Structured Data for Pricing
const structuredData = {
  "@context": "https://schema.org",
  "@type": "Product",
  "name": "QRAnalytica",
  "description": "Professional QR code analytics platform with comprehensive features",
  "offers": [
    {
      "@type": "Offer",
      "name": "Essential Plan",
      "price": "0",
      "priceCurrency": "USD",
      "description": "Perfect for basic QR code needs"
    },
    {
      "@type": "Offer",
      "name": "Professional Plan",
      "price": "149",
      "priceCurrency": "USD",
      "description": "Complete QR analytics suite with unlimited features"
    },
    {
      "@type": "Offer",
      "name": "Enterprise Plan",
      "price": "Custom",
      "priceCurrency": "USD",
      "description": "Tailored solutions for large organizations"
    }
  ]
};
---

<Layout title={title} description={description}>
  <!-- Structured Data -->
  <script type="application/ld+json" is:inline set:html={JSON.stringify(structuredData)} />

  <!-- Hero Section -->
  <section class="relative py-24 bg-gradient-to-b from-slate-50 to-white">
    <div class="container mx-auto px-4 text-center">
      <!-- Badge -->
      <div class="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-8">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        Limited Time Launch Offer
      </div>

      <!-- Main Headline -->
      <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6 max-w-4xl mx-auto leading-tight">
        Simple, Transparent
        <span class="text-primary">Pricing</span>
      </h1>

      <!-- Subheadline -->
      <p class="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
        Choose the perfect plan for your QR code analytics needs. Start free, upgrade when ready.
      </p>

      <!-- Key Benefits -->
      <div class="flex flex-wrap justify-center gap-4 mb-16">
        <div class="flex items-center px-4 py-2 bg-white rounded-full text-gray-700 text-sm font-medium shadow-sm border">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          No Credit Card Required (7 Day Free Trial)
        </div>
        <div class="flex items-center px-4 py-2 bg-white rounded-full text-gray-700 text-sm font-medium shadow-sm border">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          Instant activation
        </div>
        <div class="flex items-center px-4 py-2 bg-white rounded-full text-gray-700 text-sm font-medium shadow-sm border">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          7-day money back
        </div>
      </div>
    </div>
  </section>

  <!-- Pricing Plans -->
  <section class="py-20 bg-white">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Choose Your Plan
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Start free and upgrade as you grow. Professional plan includes everything you need.
        </p>
      </div>

      <div class="grid lg:grid-cols-3 md:grid-cols-1 gap-8 max-w-5xl mx-auto">
        <!-- Essential Plan (Free) -->
        <div class="bg-white rounded-xl border border-gray-200 p-8 hover:shadow-lg transition-all duration-300">
          <div class="text-center mb-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Essential</h3>
            <div class="mb-2">
              <span class="text-4xl font-bold text-gray-900">$0</span>
              <span class="text-gray-500 text-sm ml-1">forever</span>
            </div>
            <p class="text-gray-600 text-sm">Perfect for basic QR code needs</p>
          </div>

          <ul class="space-y-3 mb-8">
            <li class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-700">Static QR Codes (No Login)</span>
            </li>
            <li class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-700">Basic QR Types</span>
            </li>
            <li class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-700">Standard Quality</span>
            </li>
            <li class="flex items-center text-sm opacity-50">
              <svg class="w-4 h-4 text-gray-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
              <span class="text-gray-400">Analytics</span>
            </li>
            <li class="flex items-center text-sm opacity-50">
              <svg class="w-4 h-4 text-gray-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
              <span class="text-gray-400">Custom Domain</span>
            </li>
          </ul>

          <DynamicPricingButton
            plan="essential"
            client:load
          />
        </div>

        <!-- Professional Plan (TARGET - Most Popular) -->
        <div class="bg-white rounded-xl border-2 border-primary p-8 hover:shadow-xl transition-all duration-300 relative transform scale-105 shadow-lg">
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg">Popular</span>
          </div>

          <div class="text-center mb-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Professional</h3>
            <p class="text-gray-600 text-sm mb-2">Complete QR analytics suite</p>
            <p class="text-xs text-blue-600 font-medium mb-4">Over 5,000 companies trust our analytics</p>

            <div class="mb-4">
              <div class="text-sm text-gray-500 mb-1">Regular price</div>
              <div class="text-lg text-gray-400 line-through">$149</div>
            </div>

            <div class="mb-4">
              <div class="text-sm text-orange-600 font-medium mb-1">Special offer</div>
              <div class="mb-2">
                <span class="text-4xl font-bold text-gray-900">$0</span>
                <span class="text-gray-500 text-sm ml-1">for first week</span>
              </div>
              <div class="text-xs text-green-600 font-medium mb-2">No credit card required</div>
            </div>

            <div class="text-xs text-gray-500 mb-2">
              One-time $149 due after 7-day trial; no renewals
            </div>
            <div class="text-xs text-orange-600 font-medium mb-4">
              ⚡ Only 50 one-time slots left at this pricing
            </div>
          </div>

          <div class="mb-6">
            <div class="text-sm font-semibold text-gray-900 mb-4">Everything you get:</div>
            <ul class="space-y-3">
              <li class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">Everything in Essential</span>
              </li>
              <li class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">100 Dynamic QRs (Unlimited Scans)</span>
              </li>
              <li class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">Detailed Daily Scan Reports</span>
              </li>
              <li class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">Total Scans and Unique Users</span>
              </li>
              <li class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">Scans by Time of day</span>
              </li>
              <li class="flex items-center text-sm">
                <svg class="w-4 h-4 text-blue-600 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700"> Device & OS Analytics </span>
              </li>
              <li class="flex items-center text-sm">
                <svg class="w-4 h-4 text-blue-600 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700"> Geographic Insights </span>
              </li>
              <li class="flex items-center text-sm">
                <svg class="w-4 h-4 text-blue-600 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700"> Dynamic URL Updates  (Up to 3 Changes)</span>
              </li>
              <li class="flex items-center text-sm">
                <svg class="w-4 h-4 text-blue-600 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">Analytics History (Upto 30days)</span>
              </li>
              <li class="flex items-center text-sm">
                <svg class="w-4 h-4 text-blue-600 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700"> 1 Custom Domain </span>
              </li>
              <!-- multiple QR dowload formats (PNG, SVG, JPG) -->
              <li class="flex items-center text-sm">
                <svg class="w-4 h-4 text-blue-600 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700"> Multiple QR dowload formats (PNG, SVG, JPG) </span>
              </li>
            </ul>
          </div>

          <DynamicPricingButton
            plan="professional"
            client:load
          />
        </div>

        <!-- Enterprise Plan -->
        <div class="bg-white rounded-xl border border-gray-200 p-8 hover:shadow-lg transition-all duration-300">
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">Enterprise Grade</span>
          </div>

          <div class="text-center mb-6 pt-4">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Enterprise</h3>
            <p class="text-gray-600 text-sm mb-4">Tailored solutions for large organizations</p>

            <div class="mb-4">
              <div class="text-4xl font-bold text-gray-900 mb-2">Custom</div>
              <div class="text-sm text-gray-500 mb-2">solution</div>
              <div class="text-xs text-gray-600">Typical one-time investment: $199–$999</div>
            </div>
          </div>

          <div class="mb-8">
            <h4 class="text-lg font-semibold mb-4 text-center">Let's Build Your Perfect Solution</h4>
            <p class="text-gray-600 text-sm mb-6 text-center">
              Get a customized plan that scales with your needs. Our enterprise solutions offer:
            </p>
            <ul class="space-y-3 text-left text-sm">
              <li class="flex items-center text-gray-700">
                <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Custom feature development</span>
              </li>
              <li class="flex items-center text-gray-700">
                <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Dedicated support team</span>
              </li>
              <li class="flex items-center text-gray-700">
                <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Flexible pricing options</span>
              </li>
            </ul>
          </div>

          <div class="space-y-3">
            <DynamicPricingButton
              plan="enterprise"
              client:load
            />
            <button onclick="openChatForEnterprise()" class="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-200 transition-colors text-center block">
              💬 Chat with Sales
            </button>
          </div>
        </div>


      </div>

      <!-- Why Choose Professional -->
      <div class="mt-16 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 border border-blue-200">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Why Start with Professional?</h3>
          <p class="text-gray-600">Get the complete QR analytics experience with our limited-time offer</p>
        </div>

        <div class="grid md:grid-cols-3 gap-6 text-center">
          <div class="bg-white rounded-lg p-6 border">
            <div class="text-lg font-bold text-gray-900 mb-1">Essential</div>
            <div class="text-2xl font-bold text-green-600 mb-1">$0</div>
            <div class="text-xs text-gray-500">forever</div>
          </div>
          <div class="bg-gradient-to-br from-primary/10 to-secondary/10 rounded-lg p-6 border-2 border-primary relative">
            <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
              <span class="bg-blue-600 text-white px-2 py-0.5 rounded text-xs font-bold">BEST VALUE</span>
            </div>
            <div class="text-lg font-bold text-primary mb-1">Professional</div>
            <div class="text-2xl font-bold text-primary mb-1">$149</div>
            <div class="text-xs text-gray-500">one-time (after 7-day trial)</div>
          </div>
          <div class="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div class="text-lg font-bold text-yellow-400 mb-1">Enterprise</div>
            <div class="text-2xl font-bold text-yellow-400 mb-1">$499+</div>
            <div class="text-xs text-gray-400">custom solution</div>
          </div>
        </div>

        <div class="mt-8 text-center">
          <p class="text-sm text-gray-600 mb-4">
            💡 <strong>Professional</strong> gives you 100 Dynamic QR codes and advanced analytics!<br/>
            <span class="text-orange-600 font-medium">⚡ Limited Time: Try Professional free for 7 days - no credit card required</span>
          </p>
          <DynamicPricingButton
            plan="professional"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            client:load
          />
        </div>
      </div>

      <!-- All Features Included -->
      <div class="mt-12 bg-white rounded-xl p-8 shadow-sm border">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Every Plan Includes All Features</h3>
         </div>

        <div class="grid md:grid-cols-3 gap-6">
          <div>
            <h4 class="font-semibold text-gray-900 mb-4">QR Code Features</h4>
            <ul class="space-y-2 text-sm text-gray-600">
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Dynamic QR codes
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Custom design & colors
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Logo embedding
              </li>
          
            </ul>
          </div>

          <div>
            <h4 class="font-semibold text-gray-900 mb-4">Analytics Features</h4>
            <ul class="space-y-2 text-sm text-gray-600">
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Real-time tracking
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Geographic insights
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Device & OS data
              </li>
            
            </ul>
          </div>

          <div>
            <h4 class="font-semibold text-gray-900 mb-4">Support & Access</h4>
            <ul class="space-y-2 text-sm text-gray-600">
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                9AM - 9PM (IST) Support
              </li>
             <!-- Email Support  & Chat Support -->
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Email Support
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Chat Support
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- FAQ Section -->
  <section class="py-20 bg-slate-50">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Frequently Asked Questions
        </h2>
        <p class="text-lg text-gray-600">
          Everything you need to know about our pricing
        </p>
      </div>

      <div class="max-w-3xl mx-auto space-y-6">
        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">How does the Professional free trial work?</h3>
          <p class="text-gray-600">Start with Professional features completely free for 7 days - no credit card required. After the trial, you'll be charged a one-time payment of $149 to continue using all Professional features.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Are there any recurring fees?</h3>
          <p class="text-gray-600">No! After the 7-day trial, you pay just $149 once and own your Professional account forever. No monthly subscriptions or hidden charges.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">What's the difference between Essential and Professional?</h3>
          <p class="text-gray-600">Essential is free forever but only includes basic static QR codes. Professional includes 100 dynamic QR codes, detailed analytics, geographic insights, custom domain, and all advanced features for $149 one-time.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Can I cancel during the free trial?</h3>
          <p class="text-gray-600">Yes, you can cancel anytime during your 7-day trial with no charges. No credit card required to start. You'll still have access to Essential features forever.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">What happens to my QR codes if I don't upgrade?</h3>
          <p class="text-gray-600">Your static QR codes will continue working forever. Dynamic QR codes created during the trial will remain active, but you won't be able to edit them or access analytics without upgrading.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">Is there a money-back guarantee?</h3>
          <p class="text-gray-600">Yes! We offer a 7-day money-back guarantee on all paid plans. If you're not satisfied after purchasing, contact us within 7 days for a full refund.</p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="font-semibold text-gray-900 mb-2">How do I get Enterprise pricing?</h3>
          <p class="text-gray-600">Contact our sales team for custom Enterprise solutions. We'll work with you to create a plan that fits your organization's specific needs and budget.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-gradient-to-br from-blue-600 to-blue-800">
    <div class="container mx-auto px-4 text-center">
      <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
        Ready to Get Started?
      </h2>
      <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
        Start your free Professional trial today and experience the complete QR analytics suite.
      </p>

      <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
        <DynamicPricingButton
          plan="professional"
          className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors text-lg shadow-lg"
          client:load
        />
        <DynamicPricingButton
          plan="essential"
          className="inline-flex items-center px-8 py-4 bg-white/10 text-white font-semibold rounded-lg hover:bg-white/20 transition-colors text-lg border border-white/20"
          client:load
        />
      </div>

      <p class="text-white/80 text-sm">
        Free for 7 days • No credit card required • Then $149 one-time • 7-day money-back guarantee
      </p>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="py-16 bg-white">
    <div class="container mx-auto px-4 text-center">
      <div class="bg-slate-50 rounded-xl p-8 max-w-2xl mx-auto">
        <h3 class="text-2xl font-bold text-gray-900 mb-4">Need Help Choosing?</h3>
        <p class="text-gray-600 mb-6">
          Our team is here to help you find the perfect plan for your needs.
        </p>
        <a href="mailto:<EMAIL>" class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg font-semibold hover:bg-primary/90 transition-colors">
          Contact Support
        </a>
      </div>
    </div>
  </section>

  <!-- JavaScript for Enterprise Chat -->
  <script is:inline>
    function openChatForEnterprise() {
      // Check if Crisp chat is available
      if (typeof window.$crisp !== 'undefined') {
        // Open the chat
        window.$crisp.push(['do', 'chat:open']);

        // Wait a moment for the chat to open, then send the auto message
        setTimeout(() => {
          window.$crisp.push(['do', 'message:send', ['text', 'Hi! I need help with QRAnalytica. Could you please assist me?']]);
        }, 500);
      } else {
        // Fallback if Crisp is not loaded
        console.warn('Crisp chat is not available');
        // Redirect to email as fallback
        window.location.href = 'mailto:<EMAIL>?subject=Enterprise Solutions Inquiry&body=Hi, I\'m interested in learning more about QRAnalytica\'s Enterprise solutions. Could you help me with pricing and custom features?';
      }
    }
  </script>
</Layout>