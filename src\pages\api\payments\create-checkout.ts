import type { APIRoute } from 'astro';
import { getUserIdFromRequest, isAuthenticated } from '../../../lib/auth-utils';
import { getUserSubscription, getTrialStatus } from '../../../lib/subscription-utils';
import { getDatabase } from '../../../lib/database';
import {
  initializeLemonSqueezyConfig,
  createPaymentUrl,
  validateLemonSqueezyConfig
} from '../../../lib/lemonsqueezy-utils';

export const prerender = false;

interface CreateCheckoutRequest {
  plan: 'professional' | 'enterprise';
  successUrl?: string;
  cancelUrl?: string;
}

interface CreateCheckoutResponse {
  success: boolean;
  data?: {
    checkout_url: string;
    plan: string;
  };
  error?: string;
}

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Initialize LemonSqueezy configuration
    const env = locals.runtime.env;
    initializeLemonSqueezyConfig(env);

    // Validate LemonSqueezy configuration
    const configValidation = validateLemonSqueezyConfig();
    if (!configValidation.isValid) {
      console.error('LemonSqueezy configuration invalid:', configValidation.missingVars);
      return new Response(JSON.stringify({
        success: false,
        error: 'Payment system not configured properly'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get database connection
    const db = getDatabase(env);

    // Parse request body
    const body: CreateCheckoutRequest = await request.json();
    const { plan, successUrl, cancelUrl } = body;

    // Validate plan
    if (!plan || !['professional', 'enterprise'].includes(plan)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid plan specified'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Enterprise plans are handled via contact
    if (plan === 'enterprise') {
      return new Response(JSON.stringify({
        success: false,
        error: 'Enterprise plans require custom consultation. Please contact our sales team.'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check authentication
    const userId = getUserIdFromRequest(request);
    const isUserAuthenticated = isAuthenticated(request);

    if (!userId || !isUserAuthenticated) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User must be logged in to purchase'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user subscription information
    const userSubscription = await getUserSubscription(db, userId);
    
    if (!userSubscription) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User subscription information not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if user already has an active professional subscription
    if (userSubscription.subscription_plan === 'professional' && 
        userSubscription.subscription_status === 'active') {
      return new Response(JSON.stringify({
        success: false,
        error: 'User already has an active Professional subscription'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get trial status
    const trialStatus = getTrialStatus(userSubscription);

    // If user hasn't started trial yet, they should start trial first
    if (!trialStatus.hasTrialStarted) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Please start your free trial first before purchasing'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Create payment URL with prefilled email
    const checkoutUrl = createPaymentUrl(
      userSubscription.email,
      successUrl || '/dashboard?payment=success'
    );

    const response: CreateCheckoutResponse = {
      success: true,
      data: {
        checkout_url: checkoutUrl,
        plan: plan
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create checkout session'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
