/**
 * LemonSqueezy API utilities for QRAnalytica
 * Handles payment processing and webhook validation
 */

// LemonSqueezy Webhook Types (based on your payload structure)
export interface LemonSqueezyWebhookPayload {
  data: {
    id: string;
    type: "orders";
    attributes: {
      status: string;
      user_email: string;
      user_name: string;
      total: number;
      currency: string;
      created_at: string;
      updated_at: string;
      test_mode: boolean;
      customer_id: number;
      order_number: number;
      identifier: string;
      first_order_item: {
        id: number;
        product_name: string;
        variant_name: string;
        price: number;
        quantity: number;
      };
    };
  };
  meta: {
    test_mode: boolean;
    event_name: string;
    webhook_id: string;
  };
}

// LemonSqueezy Configuration
export const LEMONSQUEEZY_CONFIG = {
  // Direct payment URL for Professional plan
  PAYMENT_URL: 'https://qranalytica.lemonsqueezy.com/buy/0602844f-07d7-47f6-a828-a3426a703725',

  // Webhook configuration
  WEBHOOK_SECRET: 'brANn5jgb2F7lVbDH53ZV8fH9XVsl2SL8', // Will be populated from env

  // Product configuration
  PROFESSIONAL_PLAN: {
    name: 'Professional Plan',
    price: 199, // Updated to match the actual price from webhook
    currency: 'USD',
    description: 'Complete QR analytics suite with unlimited features'
  },

  // Default redirect URLs
  DEFAULT_SUCCESS_URL: '/dashboard?payment=success',
  DEFAULT_CANCEL_URL: '/pricing?payment=cancelled'
};

/**
 * Initialize LemonSqueezy configuration with environment variables
 */
export function initializeLemonSqueezyConfig(env: any) {
  LEMONSQUEEZY_CONFIG.WEBHOOK_SECRET = env.LEMONSQUEEZY_WEBHOOK_SECRET || '';
}

/**
 * Create a payment URL with prefilled user email
 */
export function createPaymentUrl(userEmail: string, successUrl?: string): string {
  const url = new URL(LEMONSQUEEZY_CONFIG.PAYMENT_URL);

  // Add user email as a query parameter to prefill the checkout form
  if (userEmail) {
    url.searchParams.set('checkout[email]', userEmail);
  }

  // Add success URL if provided
  if (successUrl) {
    url.searchParams.set('checkout[custom][success_url]', successUrl);
  }

  return url.toString();
}



/**
 * Verify LemonSqueezy webhook signature
 */
export function verifyWebhookSignature(payload: string, signature: string): boolean {
  if (!LEMONSQUEEZY_CONFIG.WEBHOOK_SECRET) {
    console.warn('LemonSqueezy webhook secret not configured');
    return false;
  }

  try {
    // LemonSqueezy uses HMAC-SHA256 for webhook signatures
    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha256', LEMONSQUEEZY_CONFIG.WEBHOOK_SECRET)
      .update(payload)
      .digest('hex');
    
    return signature === expectedSignature;
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    return false;
  }
}

/**
 * Process LemonSqueezy webhook payload
 */
export function processWebhookPayload(payload: LemonSqueezyWebhookPayload) {
  const { meta, data } = payload;

  return {
    eventName: meta.event_name,
    orderId: data.id,
    status: data.attributes.status,
    customerId: data.attributes.customer_id,
    total: data.attributes.total,
    currency: data.attributes.currency,
    userEmail: data.attributes.user_email,
    userName: data.attributes.user_name,
    createdAt: data.attributes.created_at,
    testMode: data.attributes.test_mode,
    orderNumber: data.attributes.order_number,
    identifier: data.attributes.identifier,
    productName: data.attributes.first_order_item.product_name
  };
}

/**
 * Validate LemonSqueezy configuration
 */
export function validateLemonSqueezyConfig(): { isValid: boolean; missingVars: string[] } {
  const requiredVars = ['WEBHOOK_SECRET'];

  const missingVars = requiredVars.filter(varName => {
    const value = LEMONSQUEEZY_CONFIG[varName as keyof typeof LEMONSQUEEZY_CONFIG];
    return !value || value === '';
  });

  return {
    isValid: missingVars.length === 0,
    missingVars
  };
}
