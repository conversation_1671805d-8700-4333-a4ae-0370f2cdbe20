import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';

interface SubscriptionStatusResponse {
  success: boolean;
  data?: {
    subscription_plan: string;
    subscription_status: string;
    trial_status: {
      hasTrialStarted: boolean;
      isTrialActive: boolean;
      isTrialExpired: boolean;
      trialDaysRemaining: number;
      trialEndDate: string | null;
      canAccessPremiumFeatures: boolean;
    };
    can_access_premium: boolean;
  };
  error?: string;
}

interface DynamicPricingButtonProps {
  plan: 'essential' | 'professional' | 'enterprise';
  className?: string;
  children?: React.ReactNode;
}

export const DynamicPricingButton: React.FC<DynamicPricingButtonProps> = ({ 
  plan, 
  className = '', 
  children 
}) => {
  const { session, status } = useAuthStore();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionStatusResponse['data'] | null>(null);
  const [loading, setLoading] = useState(false);
  const [buttonText, setButtonText] = useState('Loading...');
  const [buttonAction, setButtonAction] = useState<() => void>(() => {});

  // Check subscription status when user is authenticated
  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      checkSubscriptionStatus();
    } else if (status === 'unauthenticated') {
      // User not logged in
      updateButtonForUnauthenticated();
    }
  }, [status, session, plan]);

  const checkSubscriptionStatus = async () => {
    try {
      const response = await fetch('/api/subscription-status', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const result: SubscriptionStatusResponse = await response.json();
        if (result.success && result.data) {
          setSubscriptionData(result.data);
          updateButtonForAuthenticatedUser(result.data);
        }
      } else if (response.status === 401) {
        // User not authenticated
        updateButtonForUnauthenticated();
      }
    } catch (error) {
      console.error('Error checking subscription status:', error);
      updateButtonForUnauthenticated();
    }
  };

  const updateButtonForUnauthenticated = () => {
    switch (plan) {
      case 'essential':
        setButtonText('Get Started Free');
        setButtonAction(() => () => {
          window.location.href = '/tool/qr-code-generator';
        });
        break;
      case 'professional':
        setButtonText('Start 7-Day Free Trial →');
        setButtonAction(() => () => {
          // Redirect to login first, then to trial
          window.location.href = '/api/auth/google';
        });
        break;
      case 'enterprise':
        setButtonText('📧 Email Sales Team');
        setButtonAction(() => () => {
          window.location.href = 'mailto:<EMAIL>';
        });
        break;
    }
  };

  const updateButtonForAuthenticatedUser = (data: SubscriptionStatusResponse['data']) => {
    if (!data) return;

    const { subscription_plan, subscription_status, trial_status } = data;

    switch (plan) {
      case 'essential':
        setButtonText('Get Started Free');
        setButtonAction(() => () => {
          window.location.href = '/tool/qr-code-generator';
        });
        break;

      case 'professional':
        // User already has active professional subscription
        if (subscription_plan === 'professional' && subscription_status === 'active') {
          setButtonText('✓ Current Plan');
          setButtonAction(() => () => {
            window.location.href = '/dashboard';
          });
        }
        // User has not started trial yet
        else if (!trial_status.hasTrialStarted) {
          setButtonText('Start 7-Day Free Trial →');
          setButtonAction(() => startFreeTrial);
        }
        // User has active trial
        else if (trial_status.isTrialActive) {
          setButtonText(`Trial Active (${trial_status.trialDaysRemaining} days left)`);
          setButtonAction(() => () => {
            window.location.href = '/dashboard';
          });
        }
        // User's trial has expired or they're in trial and want to upgrade
        else if (trial_status.hasTrialStarted) {
          setButtonText('Upgrade Now →');
          setButtonAction(() => upgradeNow);
        }
        break;

      case 'enterprise':
        setButtonText('📧 Email Sales Team');
        setButtonAction(() => () => {
          window.location.href = 'mailto:<EMAIL>';
        });
        break;
    }
  };

  const startFreeTrial = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/subscription/start-trial', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();

      if (result.success) {
        // Trial started successfully, redirect to dashboard
        window.location.href = '/dashboard?trial=started';
      } else {
        alert(result.error || 'Failed to start trial');
      }
    } catch (error) {
      console.error('Error starting trial:', error);
      alert('Failed to start trial. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const upgradeNow = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/payments/create-checkout', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan: 'professional',
          successUrl: '/dashboard?payment=success',
          cancelUrl: '/pricing?payment=cancelled'
        })
      });

      const result = await response.json();

      if (result.success && result.data?.checkout_url) {
        // Redirect to LemonSqueezy checkout
        window.location.href = result.data.checkout_url;
      } else {
        alert(result.error || 'Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error creating checkout:', error);
      alert('Failed to create checkout. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Determine button style based on plan
  const getButtonStyle = () => {
    const baseClasses = 'w-full py-3 px-6 rounded-lg font-semibold transition-colors text-center block';
    
    switch (plan) {
      case 'essential':
        return `${baseClasses} bg-gray-900 text-white hover:bg-gray-800`;
      case 'professional':
        if (subscriptionData?.subscription_plan === 'professional' && 
            subscriptionData?.subscription_status === 'active') {
          return `${baseClasses} bg-green-600 text-white hover:bg-green-700`;
        }
        return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700`;
      case 'enterprise':
        return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700`;
      default:
        return `${baseClasses} bg-gray-600 text-white hover:bg-gray-700`;
    }
  };

  return (
    <button
      onClick={buttonAction}
      disabled={loading}
      className={`${getButtonStyle()} ${className} ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {loading ? 'Processing...' : (children || buttonText)}
    </button>
  );
};
