import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '../ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '../ui/tabs';
import {
  ArrowLeft,
  Globe,
  Smartphone,
  Clock,
  TrendingUp,
  Activity,
  Download
} from 'lucide-react';

import { GeographicAnalytics } from './analytics/GeographicAnalytics';
import { TimeBasedAnalytics } from './analytics/TimeBasedAnalytics';
import { DeviceAnalytics } from './analytics/DeviceAnalytics';
import { ReportingTools } from './analytics/ReportingTools';
import { ActivityFeed } from './analytics/ActivityFeed';
import { QRCodeDetails } from './analytics/QRCodeDetails';
import type { QRCode } from '../../types/dashboard';
import { analyticsApi } from '../../lib/api-utils';

interface QRAnalyticsDetailProps {
  // qrCode: QRCode;
  qrCodeId: string;
  onBack: () => void;
}



export const QRAnalyticsDetail: React.FC<QRAnalyticsDetailProps> = ({
  qrCodeId,
  onBack
}) => {
  const [selectedDateRange, setSelectedDateRange] = useState('7d');

  const handleExportData = async (format: 'csv' | 'excel') => {
    try {
      const blob = await analyticsApi.exportData(format, selectedDateRange, qrCodeId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `qr-analytics-${qrCodeId}-${selectedDateRange}.${format === 'excel' ? 'xlsx' : 'csv'}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Export error:', err);
    }
  };



  return (
    <div className="space-y-4 sm:space-y-6 w-full max-w-full overflow-hidden">
      {/* Header with Breadcrumb */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4">
        <div className="flex items-center space-x-3 sm:space-x-4">
          <Button variant="outline" onClick={onBack} className="h-9 sm:h-10">
            <ArrowLeft className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Back to Dashboard</span>
            <span className="sm:hidden">Back</span>
          </Button>
        </div>

        {/* <div className="flex items-center space-x-3">
          <Button
            onClick={() => handleExportData('excel')}
            className="bg-green-600 hover:bg-green-700 text-white flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>Export</span>
          </Button>
        </div> */}
      </div>

      {/* QR Code Details */}
      <div className="w-full max-w-full overflow-hidden">
        <QRCodeDetails qrCodeId={qrCodeId} />
      </div>

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="geographic" className="space-y-4 sm:space-y-6 w-full max-w-full">
        <div className="w-full overflow-x-auto">
          <TabsList className="grid w-full grid-cols-4 min-w-[320px] sm:min-w-0">
            <TabsTrigger value="geographic" className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-3 text-xs sm:text-sm">
              <Globe className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="truncate">
                <span className="hidden sm:inline">Geographic</span>
                <span className="sm:hidden">Geo</span>
              </span>
            </TabsTrigger>
            <TabsTrigger value="time-based" className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-3 text-xs sm:text-sm">
              <Clock className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="truncate">
                <span className="hidden sm:inline">Time Analysis</span>
                <span className="sm:hidden">Time</span>
              </span>
            </TabsTrigger>
            <TabsTrigger value="devices" className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-3 text-xs sm:text-sm">
              <Smartphone className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="truncate">Devices</span>
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-3 text-xs sm:text-sm">
              <Activity className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="truncate">Activity</span>
            </TabsTrigger>
            {/* <TabsTrigger value="reports" className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>Reports</span>
            </TabsTrigger> */}
          </TabsList>
        </div>

        <div className="w-full max-w-full overflow-hidden">
          <TabsContent value="geographic" className="space-y-4 sm:space-y-6">
            <GeographicAnalytics dateRange={selectedDateRange} qrCodeId={qrCodeId} />
          </TabsContent>

          <TabsContent value="time-based" className="space-y-4 sm:space-y-6">
            <TimeBasedAnalytics dateRange={selectedDateRange} qrCodeId={qrCodeId} />
          </TabsContent>

          <TabsContent value="devices" className="space-y-4 sm:space-y-6">
            <DeviceAnalytics dateRange={selectedDateRange} qrCodeId={qrCodeId} />
          </TabsContent>

          <TabsContent value="activity" className="space-y-4 sm:space-y-6">
            <ActivityFeed dateRange={selectedDateRange} qrCodeId={qrCodeId} />
          </TabsContent>
        </div>

        {/* <TabsContent value="reports" className="space-y-6">
          <ReportingTools
            dateRange={selectedDateRange}
            onDateRangeChange={setSelectedDateRange}
            onExport={handleExportData}
            qrCodeId={qrCodeId}
          />
        </TabsContent> */}
      </Tabs>
    </div>
  );
};

export default QRAnalyticsDetail;
