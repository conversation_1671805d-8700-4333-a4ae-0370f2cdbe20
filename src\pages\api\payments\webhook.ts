import type { APIRoute } from 'astro';
import { getDatabase } from '../../../lib/database';
import { 
  initializeLemonSqueezyConfig, 
  verifyWebhookSignature, 
  processWebhookPayload,
  type LemonSqueezyWebhookPayload 
} from '../../../lib/lemonsqueezy-utils';

export const prerender = false;

/**
 * Update user subscription status in database
 */
async function updateUserSubscription(
  db: any, 
  userId: string, 
  plan: string, 
  status: string, 
  paymentDate: string
) {
  try {
    const result = await db
      .prepare(`
        UPDATE users 
        SET subscription_plan = ?, 
            subscription_status = ?, 
            subscription_payment_date = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `)
      .bind(plan, status, paymentDate, userId)
      .run();

    console.log('User subscription updated:', { userId, plan, status, affected: result.changes });
    return result.changes > 0;
  } catch (error) {
    console.error('Error updating user subscription:', error);
    throw error;
  }
}

/**
 * Log webhook event for debugging and audit trail
 */
async function logWebhookEvent(
  db: any, 
  eventName: string, 
  orderId: string, 
  userId: string | undefined, 
  status: string,
  payload: any
) {
  try {
    // Create webhook_logs table if it doesn't exist
    await db
      .prepare(`
        CREATE TABLE IF NOT EXISTS webhook_logs (
          id TEXT PRIMARY KEY,
          event_name TEXT NOT NULL,
          order_id TEXT,
          user_id TEXT,
          status TEXT,
          payload TEXT,
          processed_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `)
      .run();

    // Insert log entry
    const logId = crypto.randomUUID();
    await db
      .prepare(`
        INSERT INTO webhook_logs (id, event_name, order_id, user_id, status, payload)
        VALUES (?, ?, ?, ?, ?, ?)
      `)
      .bind(logId, eventName, orderId, userId || null, status, JSON.stringify(payload))
      .run();

    console.log('Webhook event logged:', { logId, eventName, orderId, userId });
  } catch (error) {
    console.error('Error logging webhook event:', error);
    // Don't throw here - logging failure shouldn't break webhook processing
  }
}

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Initialize LemonSqueezy configuration
    const env = locals.runtime.env;
    initializeLemonSqueezyConfig(env);

    // Get database connection
    const db = getDatabase(env);

    // Get request body and signature
    const body = await request.text();
    const signature = request.headers.get('x-signature') || '';

    console.log('Webhook received:', { 
      hasBody: !!body, 
      hasSignature: !!signature,
      bodyLength: body.length 
    });

    // Verify webhook signature (in production)
    if (env.NODE_ENV === 'production') {
      if (!verifyWebhookSignature(body, signature)) {
        console.error('Invalid webhook signature');
        return new Response('Invalid signature', { status: 401 });
      }
    } else {
      console.log('Skipping signature verification in development mode');
    }

    // Parse webhook payload
    let payload: LemonSqueezyWebhookPayload;
    try {
      payload = JSON.parse(body);
    } catch (error) {
      console.error('Invalid JSON payload:', error);
      return new Response('Invalid JSON', { status: 400 });
    }

    // Process webhook payload
    const webhookData = processWebhookPayload(payload);
    console.log('Processed webhook data:', webhookData);

    // Log webhook event
    await logWebhookEvent(
      db,
      webhookData.eventName,
      webhookData.orderId,
      webhookData.userEmail, // Use email instead of userId
      webhookData.status,
      payload
    );

    // Handle order_created events (only event we care about)
    if (webhookData.eventName === 'order_created' && webhookData.status === 'paid') {
      console.log('Paid order created:', webhookData.orderId);

      // Find user by email and activate subscription
      try {
        // First, find the user by email
        const user = await db
          .prepare('SELECT id FROM users WHERE email = ?')
          .bind(webhookData.userEmail)
          .first();

        if (user) {
          const success = await updateUserSubscription(
            db,
            user.id as string,
            'professional',
            'active',
            webhookData.createdAt
          );

          if (success) {
            console.log('Subscription activated for user:', user.id, 'email:', webhookData.userEmail);
          } else {
            console.error('Failed to activate subscription for user:', user.id);
          }
        } else {
          console.error('User not found for email:', webhookData.userEmail);
        }
      } catch (error) {
        console.error('Error processing paid order:', error);
      }
    } else {
      console.log('Ignoring webhook event:', webhookData.eventName, 'status:', webhookData.status);
    }

    // Return success response
    return new Response(JSON.stringify({ 
      success: true, 
      event: webhookData.eventName,
      processed: true 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Webhook processing error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Webhook processing failed'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
